import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';

import zh from './locales/zh.json';
import en from './locales/en.json';
import ja from './locales/ja.json';

/**
 * 国际化配置
 * 支持中文、英文、日文三种语言
 */
const resources = {
  zh: {
    translation: zh
  },
  en: {
    translation: en
  },
  ja: {
    translation: ja
  }
};

i18n
  .use(initReactI18next)
  .init({
    resources,
    lng: 'zh', // 默认语言
    fallbackLng: 'en', // 回退语言
    
    interpolation: {
      escapeValue: false // React已经默认转义
    },
    
    detection: {
      // 从URL路径检测语言
      order: ['path', 'localStorage', 'navigator'],
      caches: ['localStorage']
    }
  });

export default i18n;
