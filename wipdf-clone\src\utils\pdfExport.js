import html2canvas from 'html2canvas';
import jsPDF from 'jspdf';
import { saveAs } from 'file-saver';

/**
 * PDF导出工具
 * 使用html2canvas将Invoice转换为图片，然后生成PDF
 */

/**
 * 将Invoice元素导出为PDF
 * @param {string} elementId - 要导出的元素ID
 * @param {string} filename - PDF文件名
 * @param {object} invoiceData - Invoice数据，用于生成文件名
 */
export const exportToPDF = async (elementId = 'invoice-preview', filename = null, invoiceData = null) => {
  try {
    // 获取要导出的元素
    const element = document.getElementById(elementId);
    if (!element) {
      throw new Error('找不到要导出的Invoice元素');
    }

    // 显示加载状态
    const loadingToast = showLoadingToast();

    // 临时隐藏不需要打印的元素
    const noPrintElements = document.querySelectorAll('.no-print');
    noPrintElements.forEach(el => {
      el.style.display = 'none';
    });

    // 配置html2canvas选项
    const canvas = await html2canvas(element, {
      scale: 2, // 提高分辨率
      useCORS: true,
      allowTaint: true,
      backgroundColor: '#ffffff',
      width: element.scrollWidth,
      height: element.scrollHeight,
      scrollX: 0,
      scrollY: 0
    });

    // 恢复隐藏的元素
    noPrintElements.forEach(el => {
      el.style.display = '';
    });

    // 获取canvas尺寸
    const imgWidth = canvas.width;
    const imgHeight = canvas.height;

    // 创建PDF (A4尺寸)
    const pdf = new jsPDF({
      orientation: imgHeight > imgWidth ? 'portrait' : 'landscape',
      unit: 'px',
      format: [imgWidth, imgHeight]
    });

    // 将canvas转换为图片并添加到PDF
    const imgData = canvas.toDataURL('image/png');
    pdf.addImage(imgData, 'PNG', 0, 0, imgWidth, imgHeight);

    // 生成文件名
    const pdfFilename = filename || generatePDFFilename(invoiceData);

    // 保存PDF
    pdf.save(pdfFilename);

    // 隐藏加载状态
    hideLoadingToast(loadingToast);

    // 显示成功提示
    showSuccessToast('PDF导出成功！');

  } catch (error) {
    console.error('PDF导出失败:', error);
    showErrorToast('PDF导出失败，请重试');
  }
};

/**
 * 生成PDF文件名
 * @param {object} invoiceData - Invoice数据
 * @returns {string} 文件名
 */
const generatePDFFilename = (invoiceData) => {
  if (!invoiceData) {
    return `invoice-${new Date().toISOString().split('T')[0]}.pdf`;
  }

  const type = invoiceData.type === 'windsurf' ? 'Windsurf' : 'Cursor';
  const date = new Date().toISOString().split('T')[0];
  const invoiceNum = invoiceData.invoiceNumber.replace(/[^a-zA-Z0-9]/g, '');
  
  return `${type}-Invoice-${invoiceNum}-${date}.pdf`;
};

/**
 * 显示加载提示
 * @returns {HTMLElement} 加载提示元素
 */
const showLoadingToast = () => {
  const toast = document.createElement('div');
  toast.className = 'fixed top-4 right-4 bg-blue-600 text-white px-6 py-3 rounded-lg shadow-lg z-50 flex items-center';
  toast.innerHTML = `
    <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
      <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
      <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
    </svg>
    正在生成PDF...
  `;
  document.body.appendChild(toast);
  return toast;
};

/**
 * 隐藏加载提示
 * @param {HTMLElement} toast - 加载提示元素
 */
const hideLoadingToast = (toast) => {
  if (toast && toast.parentNode) {
    toast.parentNode.removeChild(toast);
  }
};

/**
 * 显示成功提示
 * @param {string} message - 提示消息
 */
const showSuccessToast = (message) => {
  const toast = document.createElement('div');
  toast.className = 'fixed top-4 right-4 bg-green-600 text-white px-6 py-3 rounded-lg shadow-lg z-50 flex items-center';
  toast.innerHTML = `
    <svg class="mr-3 h-5 w-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
    </svg>
    ${message}
  `;
  document.body.appendChild(toast);
  
  // 3秒后自动消失
  setTimeout(() => {
    if (toast.parentNode) {
      toast.parentNode.removeChild(toast);
    }
  }, 3000);
};

/**
 * 显示错误提示
 * @param {string} message - 错误消息
 */
const showErrorToast = (message) => {
  const toast = document.createElement('div');
  toast.className = 'fixed top-4 right-4 bg-red-600 text-white px-6 py-3 rounded-lg shadow-lg z-50 flex items-center';
  toast.innerHTML = `
    <svg class="mr-3 h-5 w-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
    </svg>
    ${message}
  `;
  document.body.appendChild(toast);
  
  // 5秒后自动消失
  setTimeout(() => {
    if (toast.parentNode) {
      toast.parentNode.removeChild(toast);
    }
  }, 5000);
};

/**
 * 打印Invoice（使用浏览器打印功能）
 */
export const printInvoice = () => {
  // 添加打印样式
  const printStyles = `
    @media print {
      body * {
        visibility: hidden;
      }
      #invoice-preview, #invoice-preview * {
        visibility: visible;
      }
      #invoice-preview {
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
      }
      .no-print {
        display: none !important;
      }
    }
  `;
  
  // 添加样式到页面
  const styleSheet = document.createElement('style');
  styleSheet.type = 'text/css';
  styleSheet.innerText = printStyles;
  document.head.appendChild(styleSheet);
  
  // 执行打印
  window.print();
  
  // 打印完成后移除样式
  setTimeout(() => {
    document.head.removeChild(styleSheet);
  }, 1000);
};
