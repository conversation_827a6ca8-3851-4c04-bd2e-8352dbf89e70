@tailwind base;
@tailwind components;
@tailwind utilities;

@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

:root {
  font-family: Inter, system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;
}

body {
  margin: 0;
  min-height: 100vh;
  background-color: #ffffff;
  color: #1f2937;
}

/* 自定义组件样式 */
.invoice-container {
  @apply bg-white shadow-lg rounded-lg p-6 max-w-4xl mx-auto;
}

.invoice-header {
  @apply flex justify-between items-start mb-6;
}

.invoice-table {
  @apply w-full border-collapse;
}

.invoice-table th,
.invoice-table td {
  @apply border border-gray-300 px-3 py-2 text-left;
}

.invoice-table th {
  @apply bg-gray-50 font-medium;
}

/* PDF导出时的样式优化 */
@media print {
  body {
    @apply bg-white;
  }

  .no-print {
    display: none !important;
  }

  .invoice-container {
    @apply shadow-none rounded-none p-0 max-w-none;
  }
}
