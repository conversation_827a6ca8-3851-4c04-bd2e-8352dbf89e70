@tailwind base;
@tailwind components;
@tailwind utilities;

@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

:root {
  font-family: Inter, system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;
}

body {
  margin: 0;
  min-height: 100vh;
  background-color: #ffffff;
  color: #1f2937;
}

/* 自定义组件样式 */
.invoice-container {
  @apply bg-white shadow-lg rounded-lg p-6 max-w-4xl mx-auto;
}

.invoice-header {
  @apply flex justify-between items-start mb-6;
}

.invoice-table {
  @apply w-full border-collapse;
}

.invoice-table th,
.invoice-table td {
  @apply border border-gray-300 px-3 py-2 text-left;
}

.invoice-table th {
  @apply bg-gray-50 font-medium;
}

/* 响应式表格 */
.responsive-table {
  @apply w-full overflow-x-auto;
}

.responsive-table table {
  @apply min-w-full;
}

/* 加载动画 */
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

/* 渐入动画 */
.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 按钮悬停效果 */
.btn-hover {
  @apply transition-all duration-200 ease-in-out;
}

.btn-hover:hover {
  @apply transform -translate-y-0.5 shadow-lg;
}

/* PDF导出时的样式优化 */
@media print {
  body {
    @apply bg-white;
    -webkit-print-color-adjust: exact;
    color-adjust: exact;
  }

  .no-print {
    display: none !important;
  }

  .invoice-container {
    @apply shadow-none rounded-none p-0 max-w-none mx-0;
    page-break-inside: avoid;
  }

  /* 确保Invoice在打印时占满页面 */
  #invoice-preview {
    width: 100% !important;
    max-width: none !important;
    margin: 0 !important;
    padding: 20px !important;
    box-shadow: none !important;
    border-radius: 0 !important;
  }

  /* 打印时的表格样式 */
  table {
    page-break-inside: avoid;
  }

  tr {
    page-break-inside: avoid;
  }

  /* 确保颜色在打印时显示 */
  * {
    -webkit-print-color-adjust: exact;
    color-adjust: exact;
  }
}

/* 移动端优化 */
@media (max-width: 768px) {
  .invoice-container {
    @apply p-4;
  }

  /* 移动端表格滚动 */
  .mobile-scroll {
    @apply overflow-x-auto;
  }

  .mobile-scroll table {
    @apply min-w-full;
  }
}
