import { useTranslation } from 'react-i18next';
import WindsurfTemplate from './templates/WindsurfTemplate';
import CursorTemplate from './templates/CursorTemplate';

/**
 * Invoice预览组件
 * 根据数据类型显示相应的模板
 */
const InvoicePreview = ({ invoiceData }) => {
  const { t } = useTranslation();

  // 如果没有Invoice数据，显示空状态
  if (!invoiceData) {
    return (
      <div className="bg-white rounded-lg shadow-md p-8 text-center">
        <div className="mb-4">
          <svg 
            className="mx-auto h-16 w-16 text-gray-400" 
            fill="none" 
            stroke="currentColor" 
            viewBox="0 0 24 24"
          >
            <path 
              strokeLinecap="round" 
              strokeLinejoin="round" 
              strokeWidth={1} 
              d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" 
            />
          </svg>
        </div>
        <h3 className="text-lg font-medium text-gray-900 mb-2">
          {t('noInvoiceTitle')}
        </h3>
        <p className="text-gray-600 max-w-md mx-auto">
          {t('noInvoiceDesc')}
        </p>
      </div>
    );
  }

  // 根据Invoice类型渲染相应模板
  return (
    <div id="invoice-preview" className="bg-white rounded-lg shadow-md overflow-hidden">
      {invoiceData.type === 'windsurf' ? (
        <WindsurfTemplate data={invoiceData} />
      ) : (
        <CursorTemplate data={invoiceData} />
      )}
    </div>
  );
};

export default InvoicePreview;
