# 部署指南

## Vercel 部署

### 1. 准备工作

确保项目已经完成构建测试：

```bash
npm run build
npm run preview
```

### 2. 安装 Vercel CLI

```bash
npm install -g vercel
```

### 3. 登录 Vercel

```bash
vercel login
```

### 4. 部署项目

在项目根目录运行：

```bash
vercel
```

按照提示完成配置：
- 选择项目名称
- 选择团队（如果有）
- 确认项目设置

### 5. 生产部署

```bash
vercel --prod
```

## 其他部署选项

### Netlify 部署

1. 在 Netlify 控制台中连接 GitHub 仓库
2. 设置构建命令：`npm run build`
3. 设置发布目录：`dist`
4. 添加重定向规则（在 `public/_redirects` 文件中）：
   ```
   /zh /index.html 200
   /en /index.html 200
   /ja /index.html 200
   /* /index.html 200
   ```

### GitHub Pages 部署

1. 安装 gh-pages：
   ```bash
   npm install --save-dev gh-pages
   ```

2. 在 package.json 中添加部署脚本：
   ```json
   {
     "scripts": {
       "deploy": "npm run build && gh-pages -d dist"
     }
   }
   ```

3. 运行部署：
   ```bash
   npm run deploy
   ```

## 环境变量

如果需要配置环境变量，在 Vercel 控制台中添加：

- `NODE_ENV`: `production`
- 其他自定义环境变量...

## 域名配置

### 自定义域名

1. 在 Vercel 控制台中进入项目设置
2. 点击 "Domains" 选项卡
3. 添加自定义域名
4. 按照提示配置 DNS 记录

### SSL 证书

Vercel 会自动为所有域名提供免费的 SSL 证书。

## 性能优化

### 构建优化

项目已经包含以下优化：
- Vite 的代码分割
- Tailwind CSS 的 purge 功能
- 图片和资源优化

### CDN 加速

Vercel 自动提供全球 CDN 加速，无需额外配置。

## 监控和分析

### Vercel Analytics

在 Vercel 控制台中启用 Analytics 功能来监控网站性能。

### 错误监控

可以集成 Sentry 等错误监控服务：

```bash
npm install @sentry/react @sentry/tracing
```

## 故障排除

### 常见问题

1. **构建失败**
   - 检查 Node.js 版本是否兼容
   - 确保所有依赖都已正确安装

2. **路由问题**
   - 确保 vercel.json 中的重定向规则正确
   - 检查 SPA 路由配置

3. **样式问题**
   - 确保 Tailwind CSS 配置正确
   - 检查 PostCSS 配置

### 调试技巧

1. 本地测试生产构建：
   ```bash
   npm run build
   npm run preview
   ```

2. 查看构建日志：
   ```bash
   vercel logs
   ```

3. 检查部署状态：
   ```bash
   vercel ls
   ```

## 更新部署

每次推送到主分支时，Vercel 会自动重新部署。也可以手动触发部署：

```bash
vercel --prod
```
