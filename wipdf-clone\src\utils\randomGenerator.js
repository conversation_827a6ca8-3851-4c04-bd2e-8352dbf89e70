/**
 * 随机数据生成器
 * 用于生成Invoice的各种随机数据
 */

// 姓名数据
const firstNames = [
  'JOH<PERSON>', 'JAN<PERSON>', '<PERSON>CH<PERSON><PERSON>', 'SARAH', 'DAVID', 'EMILY', 'ROBE<PERSON>', '<PERSON><PERSON><PERSON><PERSON>',
  'WIL<PERSON><PERSON><PERSON>', 'ASHLE<PERSON>', 'JAM<PERSON>', 'AMANDA', 'CHRISTOPHER', 'STEPHANIE', 'DANIEL',
  'MELISSA', 'MATTHEW', 'NICOLE', 'ANTHONY', 'ELIZABETH', 'MARK', 'HEATHER',
  'DONALD', 'TIFFANY', 'STEVEN', 'MICHELLE', 'PAUL', 'AMBER', 'ANDREW', 'KIMBERLY',
  'TANAKA', 'ICHIRO', 'YAMADA', 'HANAKO', 'SATO', 'TARO', 'SUZUKI', 'YUKI'
];

const lastNames = [
  'SMITH', 'JOH<PERSON><PERSON>', 'W<PERSON><PERSON><PERSON><PERSON>', '<PERSON>OWN', 'JON<PERSON>', '<PERSON><PERSON><PERSON>', 'MILLER', '<PERSON><PERSON><PERSON>',
  '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>E<PERSON>', 'HERNANDEZ', 'LOPEZ', 'GONZALEZ', 'WILSON', 'ANDERSON',
  'THOMAS', 'TAYLOR', 'MOORE', 'JACKSON', 'MARTIN', 'LEE', 'PEREZ', 'THOMPSON',
  'WHITE', 'HARRIS', 'SANCHEZ', 'CLARK', 'RAMIREZ', 'LEWIS', 'ROBINSON'
];

// 地址数据
const streets = [
  '123 Main Street', '456 Oak Avenue', '789 Pine Road', '321 Elm Drive',
  '555 Fifth Circle', '777 Broadway', '999 Park Lane', '111 First Street',
  '222 Second Avenue', '333 Third Boulevard', '444 Fourth Way', '666 Sixth Place',
  '888 Eighth Street', '27 Rue de Rivoli', '51 Rue du Faubourg', '900 Villa Street',
  '801 West End Avenue', '15 Baker Street', '42 Wallaby Way'
];

const cities = [
  'New York, NY', 'Los Angeles, CA', 'Chicago, IL', 'Houston, TX', 'Phoenix, AZ',
  'Philadelphia, PA', 'San Antonio, TX', 'San Diego, CA', 'Dallas, TX', 'San Jose, CA',
  'Austin, TX', 'Jacksonville, FL', 'Fort Worth, TX', 'Columbus, OH', 'Charlotte, NC',
  'Seattle, WA', 'Denver, CO', 'Boston, MA', 'Nashville, TN', 'Baltimore, MD',
  'Paris, France', 'London, UK', 'Berlin, Germany', 'Tokyo, Japan', 'Sydney, Australia',
  'Toronto, Canada', 'Mexico City, Mexico', 'São Paulo, Brazil', 'Mumbai, India',
  'Beijing, China', 'Moscow, Russia', 'Cairo, Egypt', 'Lagos, Nigeria',
  'Lyon, France', 'Marseille, France', 'Manchester, UK', 'Birmingham, UK'
];

const regions = [
  'California', 'Texas', 'Florida', 'New York', 'Pennsylvania', 'Illinois',
  'Ohio', 'Georgia', 'North Carolina', 'Michigan', 'New Jersey', 'Virginia',
  'Washington', 'Arizona', 'Massachusetts', 'Tennessee', 'Indiana', 'Missouri',
  'Maryland', 'Wisconsin', 'Colorado', 'Minnesota', 'South Carolina', 'Alabama',
  'Louisiana', 'Kentucky', 'Oregon', 'Oklahoma', 'Connecticut', 'Utah',
  '北京', '上海', '广东', '浙江', '江苏', '山东', '河南', '四川', '湖北', '福建',
  'Tokyo', 'Osaka', 'Kyoto', 'Yokohama', 'Nagoya', 'Sapporo', 'Fukuoka',
  'Harajuku', 'Shibuya', 'Shinjuku', 'Tower Hamlets', 'Westminster', 'Camden'
];

const countries = [
  'United States', 'Canada', 'United Kingdom', 'France', 'Germany', 'Italy',
  'Spain', 'Netherlands', 'Belgium', 'Switzerland', 'Austria', 'Sweden',
  'Norway', 'Denmark', 'Finland', 'Australia', 'New Zealand', 'Japan',
  'South Korea', 'Singapore', 'Hong Kong', 'Taiwan', 'China', 'India',
  'Brazil', 'Mexico', 'Argentina', 'Chile', 'Colombia', 'Peru'
];

// 支付方式数据
const paymentMethods = [
  { type: 'Visa', suffix: () => Math.floor(1000 + Math.random() * 9000) },
  { type: 'MasterCard', suffix: () => Math.floor(1000 + Math.random() * 9000) },
  { type: 'American Express', suffix: () => Math.floor(1000 + Math.random() * 9000) },
  { type: 'Discover', suffix: () => Math.floor(1000 + Math.random() * 9000) },
  { type: 'PayPal', suffix: () => '' },
  { type: 'Apple Pay', suffix: () => '' }
];

/**
 * 生成随机Invoice号码
 * @returns {string} Invoice号码
 */
export const generateInvoiceNumber = () => {
  const prefix = Math.random().toString(16).substring(2, 10).toUpperCase();
  const suffix = Math.floor(Math.random() * 10000).toString().padStart(4, '0');
  return `${prefix}-${suffix}`;
};

/**
 * 生成随机收据号码
 * @returns {string} 收据号码
 */
export const generateReceiptNumber = () => {
  const part1 = Math.floor(1000 + Math.random() * 9000);
  const part2 = Math.floor(1000 + Math.random() * 9000);
  return `${part1}-${part2}`;
};

/**
 * 生成随机日期
 * @returns {string} 格式化的日期字符串
 */
export const generateRandomDate = () => {
  const start = new Date(2024, 0, 1); // 2024年1月1日
  const end = new Date(2025, 11, 31); // 2025年12月31日
  const randomTime = start.getTime() + Math.random() * (end.getTime() - start.getTime());
  const randomDate = new Date(randomTime);
  
  return randomDate.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });
};

/**
 * 生成随机支付方式
 * @returns {string} 支付方式字符串
 */
export const generatePaymentMethod = () => {
  const method = paymentMethods[Math.floor(Math.random() * paymentMethods.length)];
  const suffix = method.suffix();
  return suffix ? `${method.type} - ${suffix}` : method.type;
};

/**
 * 生成随机姓名
 * @returns {string} 完整姓名
 */
export const generateRandomName = () => {
  const firstName = firstNames[Math.floor(Math.random() * firstNames.length)];
  const lastName = lastNames[Math.floor(Math.random() * lastNames.length)];
  return `${firstName} ${lastName}`;
};

/**
 * 生成随机地址
 * @returns {object} 包含完整地址信息的对象
 */
export const generateRandomAddress = () => {
  return {
    street: streets[Math.floor(Math.random() * streets.length)],
    city: cities[Math.floor(Math.random() * cities.length)],
    region: regions[Math.floor(Math.random() * regions.length)],
    country: countries[Math.floor(Math.random() * countries.length)]
  };
};

/**
 * 生成完整的随机Invoice数据
 * @param {string} email - 收票人邮箱
 * @param {string} type - Invoice类型 ('windsurf' 或 'cursor')
 * @returns {object} 完整的Invoice数据
 */
export const generateInvoiceData = (email, type = 'windsurf') => {
  const address = generateRandomAddress();
  const date = generateRandomDate();
  const price = type === 'windsurf' ? '6.90' : '20.00';
  const product = type === 'windsurf' ? 'Windsurf Pro' : 'Cursor Pro';
  
  // 计算服务期间（一个月）
  const startDate = new Date(date);
  const endDate = new Date(startDate);
  endDate.setMonth(endDate.getMonth() + 1);
  
  const formatShortDate = (date) => {
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric'
    });
  };
  
  return {
    invoiceNumber: generateInvoiceNumber(),
    receiptNumber: generateReceiptNumber(),
    datePaid: date,
    paymentMethod: generatePaymentMethod(),
    customerName: generateRandomName(),
    customerAddress: address,
    customerEmail: email,
    product: product,
    serviceStart: formatShortDate(startDate),
    serviceEnd: formatShortDate(endDate),
    price: price,
    type: type
  };
};
