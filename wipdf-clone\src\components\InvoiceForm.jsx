import { useTranslation } from 'react-i18next';

/**
 * Invoice生成表单组件
 * 包含Invoice类型选择和邮箱输入
 */
const InvoiceForm = ({ 
  invoiceType, 
  setInvoiceType, 
  email, 
  setEmail, 
  onGenerate 
}) => {
  const { t } = useTranslation();

  /**
   * 处理表单提交
   * @param {Event} e - 表单提交事件
   */
  const handleSubmit = (e) => {
    e.preventDefault();
    onGenerate();
  };

  /**
   * 验证邮箱格式
   * @param {string} email - 邮箱地址
   * @returns {boolean} 是否有效
   */
  const isValidEmail = (email) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Invoice类型选择 */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-3">
            {t('invoiceType')}
          </label>
          <div className="space-y-3">
            <label className="flex items-center">
              <input
                type="radio"
                name="invoiceType"
                value="windsurf"
                checked={invoiceType === 'windsurf'}
                onChange={(e) => setInvoiceType(e.target.value)}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
              />
              <span className="ml-3 text-sm text-gray-700">
                {t('windsurfInvoice')}
              </span>
            </label>
            
            <label className="flex items-center">
              <input
                type="radio"
                name="invoiceType"
                value="cursor"
                checked={invoiceType === 'cursor'}
                onChange={(e) => setInvoiceType(e.target.value)}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
              />
              <span className="ml-3 text-sm text-gray-700">
                {t('cursorInvoice')}
              </span>
            </label>
          </div>
        </div>

        {/* 邮箱输入 */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            {t('emailLabel')}
            <span className="text-red-500 ml-1">{t('emailRequired')}</span>
          </label>
          <input
            type="email"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            placeholder="<EMAIL>"
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            required
          />
          {email && !isValidEmail(email) && (
            <p className="mt-1 text-sm text-red-600">
              请输入有效的邮箱地址
            </p>
          )}
        </div>

        {/* 生成按钮 */}
        <button
          type="submit"
          disabled={!email || !isValidEmail(email)}
          className="w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors"
        >
          {t('generateButton')}
        </button>
      </form>
    </div>
  );
};

export default InvoiceForm;
