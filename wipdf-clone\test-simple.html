<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WIPDF Clone - 随机Invoice生成器</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .fade-in {
            animation: fadeIn 0.3s ease-in-out;
        }
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        .invoice-demo {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
    </style>
</head>
<body class="bg-gray-50">
    <div class="min-h-screen">
        <!-- 头部 -->
        <header class="bg-white shadow-sm">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
                <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center">
                    <div>
                        <h1 class="text-2xl md:text-3xl font-bold text-gray-900">随机Invoice生成器</h1>
                        <p class="mt-2 text-gray-600">支持生成 Windsurf 和 Cursor Invoice模板的随机Invoice数据</p>
                    </div>
                    <div class="mt-4 sm:mt-0 flex space-x-2">
                        <select class="bg-white border border-gray-300 rounded-lg px-3 py-2 text-sm">
                            <option value="zh">🇨🇳 中文</option>
                            <option value="en">🇺🇸 English</option>
                            <option value="ja">🇯🇵 日本語</option>
                        </select>
                    </div>
                </div>
            </div>
        </header>

        <!-- 主要内容 -->
        <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <!-- 左侧表单 -->
                <div class="bg-white rounded-lg shadow-md p-6 fade-in">
                    <h2 class="text-lg font-semibold mb-4">生成设置</h2>
                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-3">Invoice类型</label>
                            <div class="space-y-3">
                                <label class="flex items-center">
                                    <input type="radio" name="type" value="windsurf" checked class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300">
                                    <span class="ml-3 text-sm text-gray-700">Windsurf Invoice ($6.90)</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="radio" name="type" value="cursor" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300">
                                    <span class="ml-3 text-sm text-gray-700">Cursor Invoice ($20.00)</span>
                                </label>
                            </div>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                收票人邮箱地址 <span class="text-red-500">*</span>
                            </label>
                            <input
                                type="email"
                                placeholder="<EMAIL>"
                                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                id="email-input"
                            >
                        </div>
                        <button
                            onclick="generateInvoice()"
                            class="w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors"
                        >
                            生成新Invoice
                        </button>
                    </div>
                </div>

                <!-- 右侧预览 -->
                <div class="bg-white rounded-lg shadow-md overflow-hidden fade-in">
                    <div id="invoice-preview" class="p-8">
                        <div class="text-center text-gray-500">
                            <svg class="mx-auto h-16 w-16 text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                            </svg>
                            <h3 class="text-lg font-medium text-gray-900 mb-2">暂无Invoice</h3>
                            <p class="text-gray-600 max-w-md mx-auto">请在左侧输入邮箱地址，然后点击"生成新Invoice"按钮开始创建随机Invoice</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 功能说明 -->
            <div class="mt-16 bg-white rounded-lg shadow-md p-8 fade-in">
                <h2 class="text-2xl font-bold text-gray-900 mb-8 text-center">功能说明</h2>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                    <div class="text-center">
                        <div class="flex justify-center mb-4">
                            <svg class="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                            </svg>
                        </div>
                        <h3 class="text-lg font-semibold text-gray-900 mb-2">随机数据生成</h3>
                        <p class="text-gray-600 text-sm leading-relaxed">自动生成Invoice号码、收据号码、支付方式、收票人信息和账单日期</p>
                    </div>
                    <div class="text-center">
                        <div class="flex justify-center mb-4">
                            <svg class="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                            </svg>
                        </div>
                        <h3 class="text-lg font-semibold text-gray-900 mb-2">PDF导出</h3>
                        <p class="text-gray-600 text-sm leading-relaxed">点击"打印/保存PDF"按钮可以将Invoice保存为PDF文件</p>
                    </div>
                    <div class="text-center">
                        <div class="flex justify-center mb-4">
                            <svg class="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                            </svg>
                        </div>
                        <h3 class="text-lg font-semibold text-gray-900 mb-2">多种模板</h3>
                        <p class="text-gray-600 text-sm leading-relaxed">支持 Windsurf 和 Cursor 两种Invoice模板，完全保持原始样式和布局</p>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script>
        // 简单的演示功能
        function generateInvoice() {
            const email = document.getElementById('email-input').value;
            const type = document.querySelector('input[name="type"]:checked').value;

            if (!email) {
                alert('请输入邮箱地址');
                return;
            }

            // 生成随机数据
            const invoiceData = {
                invoiceNumber: generateRandomString(8) + '-' + Math.floor(Math.random() * 10000),
                receiptNumber: Math.floor(1000 + Math.random() * 9000) + '-' + Math.floor(1000 + Math.random() * 9000),
                datePaid: new Date().toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' }),
                paymentMethod: 'Visa - ' + Math.floor(1000 + Math.random() * 9000),
                customerName: 'JOHN SMITH',
                customerEmail: email,
                product: type === 'windsurf' ? 'Windsurf Pro' : 'Cursor Pro',
                price: type === 'windsurf' ? '6.90' : '20.00',
                type: type
            };

            // 显示Invoice
            displayInvoice(invoiceData);
        }

        function generateRandomString(length) {
            return Math.random().toString(16).substring(2, 2 + length).toUpperCase();
        }

        function displayInvoice(data) {
            const preview = document.getElementById('invoice-preview');
            const logoColor = data.type === 'windsurf' ? 'bg-blue-600' : 'bg-indigo-600';
            const logoText = data.type === 'windsurf' ? 'Windsurf' : 'Cursor';

            preview.innerHTML = `
                <div class="fade-in">
                    <!-- 头部 -->
                    <div class="flex flex-col sm:flex-row justify-between items-start mb-6">
                        <h1 class="text-2xl font-bold text-gray-900 mb-4 sm:mb-0">Receipt</h1>
                        <div class="${logoColor} text-white px-4 py-2 rounded text-sm font-bold">${logoText}</div>
                    </div>

                    <!-- Invoice信息 -->
                    <div class="mb-6">
                        <table class="w-full">
                            <tr class="border-b border-gray-200">
                                <td class="py-2 text-sm font-medium text-gray-700 w-1/3">Invoice number</td>
                                <td class="py-2 text-sm text-gray-900">${data.invoiceNumber}</td>
                            </tr>
                            <tr class="border-b border-gray-200">
                                <td class="py-2 text-sm font-medium text-gray-700">Receipt number</td>
                                <td class="py-2 text-sm text-gray-900">${data.receiptNumber}</td>
                            </tr>
                            <tr class="border-b border-gray-200">
                                <td class="py-2 text-sm font-medium text-gray-700">Date paid</td>
                                <td class="py-2 text-sm text-gray-900">${data.datePaid}</td>
                            </tr>
                            <tr class="border-b border-gray-200">
                                <td class="py-2 text-sm font-medium text-gray-700">Payment method</td>
                                <td class="py-2 text-sm text-gray-900">${data.paymentMethod}</td>
                            </tr>
                        </table>
                    </div>

                    <!-- 客户信息 -->
                    <div class="mb-6">
                        <div class="font-bold text-gray-900 mb-2">Bill to</div>
                        <div class="text-sm text-gray-600 space-y-1">
                            <div>${data.customerName}</div>
                            <div>${data.customerEmail}</div>
                        </div>
                    </div>

                    <!-- 金额 -->
                    <h2 class="text-xl font-semibold text-gray-900 mb-4">$${data.price} paid on ${data.datePaid}</h2>

                    <!-- 服务详情 -->
                    <div class="mb-6 overflow-x-auto">
                        <table class="w-full border border-gray-300">
                            <thead>
                                <tr class="bg-gray-50">
                                    <th class="border border-gray-300 px-3 py-2 text-left text-sm font-medium text-gray-700">Description</th>
                                    <th class="border border-gray-300 px-3 py-2 text-left text-sm font-medium text-gray-700">Qty</th>
                                    <th class="border border-gray-300 px-3 py-2 text-left text-sm font-medium text-gray-700">Unit price</th>
                                    <th class="border border-gray-300 px-3 py-2 text-left text-sm font-medium text-gray-700">Amount</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td class="border border-gray-300 px-3 py-2">
                                        <div class="font-medium text-gray-900">${data.product}</div>
                                        <div class="text-sm text-gray-600">Jan 15 - Feb 15, 2025</div>
                                    </td>
                                    <td class="border border-gray-300 px-3 py-2 text-sm text-gray-900">1</td>
                                    <td class="border border-gray-300 px-3 py-2 text-sm text-gray-900">$${data.price}</td>
                                    <td class="border border-gray-300 px-3 py-2 text-sm text-gray-900">$${data.price}</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    <!-- 总计 -->
                    <div class="mb-6">
                        <table class="w-full max-w-md ml-auto">
                            <tr class="border-b border-gray-200">
                                <td class="py-2 text-sm font-medium text-gray-700 text-right pr-4">Subtotal</td>
                                <td class="py-2 text-sm text-gray-900 text-right">$${data.price}</td>
                            </tr>
                            <tr class="border-b border-gray-200">
                                <td class="py-2 text-sm font-medium text-gray-700 text-right pr-4">Total</td>
                                <td class="py-2 text-sm text-gray-900 text-right">$${data.price}</td>
                            </tr>
                            <tr class="border-b-2 border-gray-900">
                                <td class="py-2 text-sm font-bold text-gray-900 text-right pr-4">Amount paid</td>
                                <td class="py-2 text-sm font-bold text-gray-900 text-right">$${data.price}</td>
                            </tr>
                        </table>
                    </div>

                    <!-- 页脚 -->
                    <div class="text-center text-sm text-gray-600">
                        <div>${data.receiptNumber} - $${data.price} paid on ${data.datePaid}</div>
                        <div>Page 1 of 1</div>
                    </div>

                    <!-- 操作按钮 -->
                    <div class="mt-6 flex justify-center space-x-4 no-print">
                        <button onclick="window.print()" class="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors">
                            打印
                        </button>
                        <button onclick="alert('PDF导出功能需要在React应用中使用')" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                            保存PDF
                        </button>
                    </div>
                </div>
            `;
        }
    </script>

    <style>
        @media print {
            .no-print { display: none !important; }
            body { background: white !important; }
            #invoice-preview {
                width: 100% !important;
                max-width: none !important;
                margin: 0 !important;
                padding: 20px !important;
            }
        }
    </style>
</body>
</html>
