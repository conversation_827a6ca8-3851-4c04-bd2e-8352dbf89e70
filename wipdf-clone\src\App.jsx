import { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import './i18n';
import { generateInvoiceData } from './utils/randomGenerator';
import { exportToPDF, printInvoice } from './utils/pdfExport';
import LanguageSelector from './components/LanguageSelector';
import InvoiceForm from './components/InvoiceForm';
import InvoicePreview from './components/InvoicePreview';
import FeatureSection from './components/FeatureSection';

/**
 * 主应用组件
 * 管理整个应用的状态和布局
 */
function App() {
  const { t } = useTranslation();
  const [invoiceData, setInvoiceData] = useState(null);
  const [invoiceType, setInvoiceType] = useState('windsurf');
  const [email, setEmail] = useState('');

  /**
   * 生成新的Invoice数据
   */
  const handleGenerateInvoice = () => {
    if (!email.trim()) {
      alert('请输入邮箱地址');
      return;
    }

    const newInvoiceData = generateInvoiceData(email, invoiceType);
    setInvoiceData(newInvoiceData);
  };

  /**
   * 处理PDF导出
   */
  const handleExportPDF = () => {
    if (!invoiceData) {
      alert('请先生成Invoice');
      return;
    }
    exportToPDF('invoice-preview', null, invoiceData);
  };

  /**
   * 处理打印
   */
  const handlePrint = () => {
    if (!invoiceData) {
      alert('请先生成Invoice');
      return;
    }
    printInvoice();
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 头部区域 */}
      <header className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex justify-between items-center">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">
                {t('title')}
              </h1>
              <p className="mt-2 text-gray-600">
                {t('subtitle')}
              </p>
            </div>
            <div className="flex items-center space-x-4">
              <LanguageSelector />
              {invoiceData && (
                <div className="flex space-x-2 no-print">
                  <button
                    onClick={handlePrint}
                    className="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors flex items-center"
                  >
                    <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z" />
                    </svg>
                    打印
                  </button>
                  <button
                    onClick={handleExportPDF}
                    className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center"
                  >
                    <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                    {t('printPdfButton')}
                  </button>
                </div>
              )}
            </div>
          </div>
        </div>
      </header>

      {/* 主要内容区域 */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* 左侧：表单区域 */}
          <div className="space-y-6">
            <InvoiceForm
              invoiceType={invoiceType}
              setInvoiceType={setInvoiceType}
              email={email}
              setEmail={setEmail}
              onGenerate={handleGenerateInvoice}
            />
          </div>

          {/* 右侧：预览区域 */}
          <div>
            <InvoicePreview invoiceData={invoiceData} />
          </div>
        </div>

        {/* 功能说明区域 */}
        <div className="mt-16">
          <FeatureSection />
        </div>
      </main>
    </div>
  );
}

export default App;
