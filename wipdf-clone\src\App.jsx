import { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import './i18n';
import { generateInvoiceData } from './utils/randomGenerator';
import LanguageSelector from './components/LanguageSelector';
import InvoiceForm from './components/InvoiceForm';
import InvoicePreview from './components/InvoicePreview';
import FeatureSection from './components/FeatureSection';

/**
 * 主应用组件
 * 管理整个应用的状态和布局
 */
function App() {
  const { t } = useTranslation();
  const [invoiceData, setInvoiceData] = useState(null);
  const [invoiceType, setInvoiceType] = useState('windsurf');
  const [email, setEmail] = useState('');

  /**
   * 生成新的Invoice数据
   */
  const handleGenerateInvoice = () => {
    if (!email.trim()) {
      alert('请输入邮箱地址');
      return;
    }

    const newInvoiceData = generateInvoiceData(email, invoiceType);
    setInvoiceData(newInvoiceData);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 头部区域 */}
      <header className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex justify-between items-center">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">
                {t('title')}
              </h1>
              <p className="mt-2 text-gray-600">
                {t('subtitle')}
              </p>
            </div>
            <div className="flex items-center space-x-4">
              <LanguageSelector />
              {invoiceData && (
                <button className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors no-print">
                  {t('printPdfButton')}
                </button>
              )}
            </div>
          </div>
        </div>
      </header>

      {/* 主要内容区域 */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* 左侧：表单区域 */}
          <div className="space-y-6">
            <InvoiceForm
              invoiceType={invoiceType}
              setInvoiceType={setInvoiceType}
              email={email}
              setEmail={setEmail}
              onGenerate={handleGenerateInvoice}
            />
          </div>

          {/* 右侧：预览区域 */}
          <div>
            <InvoicePreview invoiceData={invoiceData} />
          </div>
        </div>

        {/* 功能说明区域 */}
        <div className="mt-16">
          <FeatureSection />
        </div>
      </main>
    </div>
  );
}

export default App;
