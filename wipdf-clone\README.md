# WIPDF Clone - 随机Invoice生成器

一个完全复制 [wipdf.vercel.app](https://wipdf.vercel.app/zh) 功能的随机Invoice生成器，支持生成 Windsurf 和 Cursor 两种Invoice模板。

## ✨ 功能特性

- 🎲 **随机数据生成** - 自动生成Invoice号码、收据号码、支付方式、收票人信息和账单日期
- 📄 **多种模板** - 支持 Windsurf ($6.90) 和 Cursor ($20.00) 两种Invoice模板
- 🌍 **多语言支持** - 支持中文、英文、日文三种语言
- 📱 **响应式设计** - 完美适配桌面端和移动端
- 🖨️ **PDF导出** - 支持打印和保存为PDF文件
- 🎨 **精确复制** - 完全保持原网站的样式和布局

## 🚀 快速开始

### 环境要求

- Node.js 18+ (推荐 20+)
- npm 或 yarn

### 安装依赖

```bash
npm install
```

### 启动开发服务器

```bash
npm run dev
```

### 构建生产版本

```bash
npm run build
```

### 预览生产版本

```bash
npm run preview
```

## 📁 项目结构

```
wipdf-clone/
├── src/
│   ├── components/           # React组件
│   │   ├── templates/       # Invoice模板
│   │   │   ├── WindsurfTemplate.jsx
│   │   │   └── CursorTemplate.jsx
│   │   ├── LanguageSelector.jsx
│   │   ├── InvoiceForm.jsx
│   │   ├── InvoicePreview.jsx
│   │   └── FeatureSection.jsx
│   ├── locales/             # 多语言文件
│   │   ├── zh.json         # 中文
│   │   ├── en.json         # 英文
│   │   └── ja.json         # 日文
│   ├── utils/              # 工具函数
│   │   ├── randomGenerator.js  # 随机数据生成
│   │   └── pdfExport.js       # PDF导出
│   ├── i18n.js             # 国际化配置
│   ├── App.jsx             # 主应用组件
│   └── main.jsx            # 应用入口
├── test-simple.html        # 简单测试页面
├── PLANNING.md             # 项目规划文档
└── TASK.md                 # 任务管理文档
```

## 🛠️ 技术栈

- **前端框架**: React 19 + Vite 5
- **样式**: Tailwind CSS 4
- **国际化**: react-i18next
- **PDF导出**: html2canvas + jsPDF
- **文件保存**: file-saver

## 📖 使用说明

1. **选择Invoice类型** - 在左侧表单中选择 Windsurf 或 Cursor 模板
2. **输入邮箱地址** - 填写收票人的邮箱地址（必填）
3. **生成Invoice** - 点击"生成新Invoice"按钮创建随机Invoice
4. **导出PDF** - 点击"打印/保存PDF"按钮将Invoice保存为PDF文件

## 🌐 多语言支持

应用支持以下语言：
- 🇨🇳 中文 (zh)
- 🇺🇸 英文 (en)
- 🇯🇵 日文 (ja)

语言切换通过右上角的语言选择器实现。

## 📱 响应式设计

- **桌面端**: 完整的双栏布局，左侧表单，右侧预览
- **平板端**: 自适应布局，保持良好的用户体验
- **移动端**: 单栏布局，表格支持横向滚动

## 🖨️ PDF导出功能

- **打印功能**: 使用浏览器原生打印功能
- **PDF保存**: 使用html2canvas将Invoice转换为图片，再生成PDF
- **文件命名**: 自动生成包含类型、Invoice号码和日期的文件名
- **打印优化**: 专门的打印样式，隐藏不必要的UI元素

## 🎨 样式特性

- **精确复制**: 完全复制原网站的视觉设计和布局
- **动画效果**: 渐入动画和悬停效果
- **颜色主题**: Windsurf使用蓝色主题，Cursor使用紫色主题
- **字体**: 使用Inter字体确保最佳阅读体验

## 🔧 开发说明

### 添加新的Invoice模板

1. 在 `src/components/templates/` 目录下创建新的模板组件
2. 在 `src/utils/randomGenerator.js` 中添加对应的数据生成逻辑
3. 在 `InvoicePreview.jsx` 中添加模板选择逻辑
4. 更新语言文件添加新的文本

### 添加新语言

1. 在 `src/locales/` 目录下创建新的语言文件
2. 在 `src/i18n.js` 中添加语言配置
3. 在 `LanguageSelector.jsx` 中添加语言选项

## 📄 许可证

MIT License

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

## 📞 联系

如有问题或建议，请通过 GitHub Issues 联系。
