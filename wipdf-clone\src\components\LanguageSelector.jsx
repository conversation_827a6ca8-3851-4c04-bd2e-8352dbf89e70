import { useTranslation } from 'react-i18next';

/**
 * 语言选择器组件
 * 支持中文、英文、日文切换
 */
const LanguageSelector = () => {
  const { i18n } = useTranslation();

  const languages = [
    { code: 'zh', label: '🇨🇳 中文' },
    { code: 'en', label: '🇺🇸 English' },
    { code: 'ja', label: '🇯🇵 日本語' }
  ];

  /**
   * 处理语言切换
   * @param {string} languageCode - 语言代码
   */
  const handleLanguageChange = (languageCode) => {
    i18n.changeLanguage(languageCode);
    
    // 更新URL路径
    const currentPath = window.location.pathname;
    const newPath = `/${languageCode}`;
    
    // 使用history API更新URL，但不刷新页面
    window.history.pushState({}, '', newPath);
  };

  return (
    <div className="relative">
      <select
        value={i18n.language}
        onChange={(e) => handleLanguageChange(e.target.value)}
        className="appearance-none bg-white border border-gray-300 rounded-lg px-4 py-2 pr-8 text-sm font-medium text-gray-700 hover:border-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
      >
        {languages.map((lang) => (
          <option key={lang.code} value={lang.code}>
            {lang.label}
          </option>
        ))}
      </select>
      
      {/* 自定义下拉箭头 */}
      <div className="absolute inset-y-0 right-0 flex items-center px-2 pointer-events-none">
        <svg className="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
        </svg>
      </div>
    </div>
  );
};

export default LanguageSelector;
