import { useTranslation } from 'react-i18next';

/**
 * Windsurf Invoice模板组件
 * 精确复制原网站的Windsurf Invoice样式
 */
const WindsurfTemplate = ({ data }) => {
  const { t } = useTranslation();

  if (!data) return null;

  return (
    <div className="p-8 bg-white">
      {/* 头部区域 */}
      <div className="flex justify-between items-start mb-8">
        <h1 className="text-3xl font-bold text-gray-900">
          {t('receipt')}
        </h1>
        <div className="text-right">
          {/* Windsurf Logo 占位 */}
          <div className="w-24 h-10 bg-blue-600 rounded flex items-center justify-center text-white font-bold text-sm">
            Windsurf
          </div>
        </div>
      </div>

      {/* Invoice信息表格 */}
      <div className="mb-8">
        <table className="w-full">
          <tbody>
            <tr className="border-b border-gray-200">
              <td className="py-3 text-sm font-medium text-gray-700 w-1/3">
                {t('invoiceNumber')}
              </td>
              <td className="py-3 text-sm text-gray-900">
                {data.invoiceNumber}
              </td>
            </tr>
            <tr className="border-b border-gray-200">
              <td className="py-3 text-sm font-medium text-gray-700">
                {t('receiptNumber')}
              </td>
              <td className="py-3 text-sm text-gray-900">
                {data.receiptNumber}
              </td>
            </tr>
            <tr className="border-b border-gray-200">
              <td className="py-3 text-sm font-medium text-gray-700">
                {t('datePaid')}
              </td>
              <td className="py-3 text-sm text-gray-900">
                {data.datePaid}
              </td>
            </tr>
            <tr className="border-b border-gray-200">
              <td className="py-3 text-sm font-medium text-gray-700">
                {t('paymentMethod')}
              </td>
              <td className="py-3 text-sm text-gray-900">
                {data.paymentMethod}
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      {/* 公司和客户信息 */}
      <div className="grid grid-cols-2 gap-8 mb-8">
        {/* Windsurf公司信息 */}
        <div>
          <div className="font-bold text-gray-900 mb-2">Windsurf</div>
          <div className="text-sm text-gray-600 space-y-1">
            <div>900 Villa Street</div>
            <div>Mountain View, California 94041</div>
            <div>United States</div>
            <div><EMAIL></div>
            <div>EU OSS VAT EU372077851</div>
          </div>
        </div>

        {/* 客户信息 */}
        <div>
          <div className="font-bold text-gray-900 mb-2">{t('billTo')}</div>
          <div className="text-sm text-gray-600 space-y-1">
            <div>{data.customerName}</div>
            <div>{data.customerAddress.street}</div>
            <div>{data.customerAddress.city}</div>
            <div>{data.customerAddress.region}</div>
            <div>{data.customerAddress.country}</div>
            <div>{data.customerEmail}</div>
          </div>
        </div>
      </div>

      {/* 支付金额标题 */}
      <h2 className="text-xl font-semibold text-gray-900 mb-6">
        ${data.price} {t('paidOn')} {data.datePaid}
      </h2>

      {/* 服务详情表格 */}
      <div className="mb-6">
        <table className="w-full border border-gray-300">
          <thead>
            <tr className="bg-gray-50">
              <th className="border border-gray-300 px-4 py-3 text-left text-sm font-medium text-gray-700">
                {t('description')}
              </th>
              <th className="border border-gray-300 px-4 py-3 text-left text-sm font-medium text-gray-700">
                {t('qty')}
              </th>
              <th className="border border-gray-300 px-4 py-3 text-left text-sm font-medium text-gray-700">
                {t('unitPrice')}
              </th>
              <th className="border border-gray-300 px-4 py-3 text-left text-sm font-medium text-gray-700">
                {t('amount')}
              </th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td className="border border-gray-300 px-4 py-3">
                <div className="font-medium text-gray-900">{data.product}</div>
                <div className="text-sm text-gray-600">
                  {data.serviceStart} - {data.serviceEnd}, 2025
                </div>
              </td>
              <td className="border border-gray-300 px-4 py-3 text-sm text-gray-900">1</td>
              <td className="border border-gray-300 px-4 py-3 text-sm text-gray-900">${data.price}</td>
              <td className="border border-gray-300 px-4 py-3 text-sm text-gray-900">${data.price}</td>
            </tr>
          </tbody>
        </table>
      </div>

      {/* 总计表格 */}
      <div className="mb-8">
        <table className="w-full max-w-md ml-auto">
          <tbody>
            <tr className="border-b border-gray-200">
              <td className="py-2 text-sm font-medium text-gray-700 text-right pr-4">
                {t('subtotal')}
              </td>
              <td className="py-2 text-sm text-gray-900 text-right">${data.price}</td>
            </tr>
            <tr className="border-b border-gray-200">
              <td className="py-2 text-sm font-medium text-gray-700 text-right pr-4">
                {t('total')}
              </td>
              <td className="py-2 text-sm text-gray-900 text-right">${data.price}</td>
            </tr>
            <tr className="border-b-2 border-gray-900">
              <td className="py-2 text-sm font-bold text-gray-900 text-right pr-4">
                {t('amountPaid')}
              </td>
              <td className="py-2 text-sm font-bold text-gray-900 text-right">${data.price}</td>
            </tr>
          </tbody>
        </table>
      </div>

      {/* 页脚 */}
      <div className="text-center text-sm text-gray-600 space-y-1">
        <div>{data.receiptNumber} - ${data.price} {t('paidOn')} {data.datePaid}</div>
        <div>{t('pageOf')}</div>
      </div>
    </div>
  );
};

export default WindsurfTemplate;
